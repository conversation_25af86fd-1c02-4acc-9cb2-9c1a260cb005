{"name": "bba-dify-frontend", "version": "1.0.0", "description": "Dify Frontend Chat Interface", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"vue": "^3.4.0", "element-plus": "^2.4.0", "@element-plus/icons-vue": "^2.3.0", "axios": "^1.6.0", "marked": "^11.0.0", "highlight.js": "^11.9.0", "uuid": "^9.0.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.0", "vite": "^5.0.0", "eslint": "^8.55.0", "eslint-plugin-vue": "^9.19.0", "@vue/eslint-config-prettier": "^8.0.0", "prettier": "^3.1.0", "unplugin-auto-import": "^0.17.0", "unplugin-vue-components": "^0.26.0"}}