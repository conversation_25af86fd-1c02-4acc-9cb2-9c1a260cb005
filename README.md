# Dify Frontend Chat Interface

基于 Vue.js 3 + Element Plus 的 Dify 前端对话界面，提供完整的 AI 对话功能。

## 功能特性

- 🎨 **现代化 UI**: 基于 Element Plus 的美观界面设计
- 💬 **实时对话**: 支持流式响应，实时显示 AI 回复
- 📝 **Markdown 支持**: 完整的 Markdown 渲染和代码高亮
- 📱 **响应式设计**: 适配桌面和移动设备
- 🔄 **消息操作**: 支持复制、重新生成等操作
- ⚡ **快捷输入**: 提供常用问题快捷按钮
- 🎯 **打字效果**: 模拟真实的打字动画效果

## 技术栈

- **前端框架**: Vue.js 3
- **UI 组件库**: Element Plus
- **构建工具**: Vite
- **HTTP 客户端**: Axios
- **Markdown 渲染**: Marked
- **代码高亮**: Highlight.js

## 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 配置环境变量

复制 `.env.example` 到 `.env` 并配置您的 Dify API Key：

```bash
cp .env.example .env
```

编辑 `.env` 文件：

```env
VITE_DIFY_API_BASE=https://api.dify.ai/v1
VITE_DIFY_API_KEY=your-dify-api-key-here
```

### 3. 启动开发服务器

```bash
npm run dev
```

访问 http://localhost:3000 查看应用。

### 4. 构建生产版本

```bash
npm run build
```

## 项目结构

```
src/
├── components/          # Vue 组件
│   ├── ChatContainer.vue    # 主聊天容器
│   ├── MessageItem.vue      # 消息项组件
│   ├── MessageInput.vue     # 消息输入组件
│   └── TypingIndicator.vue  # 打字指示器
├── services/           # 服务层
│   └── chatService.js      # 聊天 API 服务
├── config/             # 配置文件
│   └── index.js           # 应用配置
├── App.vue             # 根组件
└── main.js             # 应用入口
```

## 配置说明

### Dify API 配置

在 `src/services/chatService.js` 中配置您的 Dify API：

```javascript
const DIFY_API_BASE = 'https://api.dify.ai/v1'
const API_KEY = 'your-dify-api-key'
```

### 功能配置

在 `src/config/index.js` 中可以配置各种功能开关：

```javascript
features: {
  fileUpload: false,        // 文件上传功能
  voiceInput: false,        // 语音输入功能
  messageRegenerate: true,  // 消息重新生成
  messageCopy: true,        // 消息复制
  conversationHistory: true // 对话历史
}
```

## 使用说明

### 基本对话

1. 在输入框中输入您的问题
2. 点击发送按钮或按 Ctrl+Enter 发送
3. AI 将以流式方式回复您的问题

### 快捷操作

- 点击快捷问题按钮快速输入常见问题
- 使用消息操作按钮复制或重新生成回复
- 支持 Markdown 格式的富文本显示

### 键盘快捷键

- `Ctrl + Enter`: 发送消息
- `Enter`: 换行

## 自定义样式

您可以通过修改 CSS 变量来自定义界面样式：

```css
:root {
  --primary-color: #409EFF;
  --border-radius: 12px;
  --chat-max-width: 800px;
}
```

## API 集成

### Dify API 接口

项目使用 Dify 的聊天 API，支持：

- 流式响应
- 对话历史
- 用户会话管理

### 模拟模式

当 API Key 未配置或 API 不可用时，系统会自动切换到模拟模式，提供演示功能。

## 部署

### 静态部署

构建后的文件可以部署到任何静态文件服务器：

```bash
npm run build
# 将 dist/ 目录部署到您的服务器
```

### Docker 部署

```dockerfile
FROM nginx:alpine
COPY dist/ /usr/share/nginx/html/
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

## 开发指南

### 添加新组件

1. 在 `src/components/` 目录下创建新组件
2. 在需要的地方导入并使用
3. 遵循 Vue 3 Composition API 规范

### 扩展 API 服务

在 `src/services/chatService.js` 中添加新的 API 方法：

```javascript
async newApiMethod() {
  // 实现新的 API 调用
}
```

## 故障排除

### 常见问题

1. **API 调用失败**: 检查 API Key 配置和网络连接
2. **样式异常**: 确保 Element Plus 样式正确加载
3. **构建失败**: 检查 Node.js 版本（推荐 16+）

### 调试模式

启用开发者工具查看详细日志：

```javascript
// 在浏览器控制台中
localStorage.setItem('debug', 'true')
```

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 发起 Pull Request

## 许可证

MIT License

## 联系方式

如有问题或建议，请提交 Issue 或联系开发团队。
