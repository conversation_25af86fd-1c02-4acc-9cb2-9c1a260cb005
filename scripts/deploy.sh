#!/bin/bash

echo "🚀 部署 Dify Frontend Chat Interface..."

# 检查是否已构建
if [ ! -d "dist" ]; then
    echo "📦 构建项目..."
    npm run build
fi

echo "📋 选择部署方式:"
echo "1) 本地预览 (http://localhost:4173)"
echo "2) 部署到 Nginx"
echo "3) 部署到 Apache"
echo "4) 生成 Docker 镜像"
echo "5) 部署到云服务 (Vercel/Netlify)"

read -p "请选择 (1-5): " choice

case $choice in
    1)
        echo "🌐 启动本地预览服务器..."
        npm run preview
        ;;
    2)
        echo "📁 复制文件到 Nginx 目录..."
        sudo cp -r dist/* /var/www/html/
        echo "✅ 部署完成! 访问 http://localhost"
        ;;
    3)
        echo "📁 复制文件到 Apache 目录..."
        sudo cp -r dist/* /var/www/html/
        echo "✅ 部署完成! 访问 http://localhost"
        ;;
    4)
        echo "🐳 构建 Docker 镜像..."
        docker build -f docker/Dockerfile -t dify-frontend:latest .
        echo "✅ Docker 镜像构建完成!"
        echo "运行命令: docker run -p 3000:80 dify-frontend:latest"
        ;;
    5)
        echo "☁️ 云服务部署说明:"
        echo ""
        echo "Vercel 部署:"
        echo "1. 安装 Vercel CLI: npm i -g vercel"
        echo "2. 运行: vercel --prod"
        echo ""
        echo "Netlify 部署:"
        echo "1. 安装 Netlify CLI: npm i -g netlify-cli"
        echo "2. 运行: netlify deploy --prod --dir=dist"
        echo ""
        echo "GitHub Pages 部署:"
        echo "1. 推送代码到 GitHub"
        echo "2. 在仓库设置中启用 GitHub Pages"
        echo "3. 选择 'dist' 目录作为源"
        ;;
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac
