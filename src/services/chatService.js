import axios from 'axios'
import { config } from '../config/index.js'

// Dify API 配置
const DIFY_API_BASE = config.dify.apiBase
const API_KEY = config.dify.apiKey

// 创建 axios 实例
const apiClient = axios.create({
  baseURL: DIFY_API_BASE,
  headers: {
    'Authorization': `Bearer ${API_KEY}`,
    'Content-Type': 'application/json'
  },
  timeout: config.dify.timeout
})

// 聊天服务类
class ChatService {
  constructor() {
    this.conversationId = null
    this.userId = 'user-' + Date.now() // 简单的用户ID生成
  }

  /**
   * 发送消息到 Dify API
   * @param {string} message - 用户消息
   * @param {function} onChunk - 流式响应回调函数
   * @returns {Promise}
   */
  async sendMessage(message, onChunk) {
    try {
      const response = await apiClient.post('/chat-messages', {
        inputs: {},
        query: message,
        response_mode: 'streaming',
        conversation_id: this.conversationId,
        user: this.userId
      }, {
        responseType: 'stream'
      })

      return new Promise((resolve, reject) => {
        let buffer = ''
        
        response.data.on('data', (chunk) => {
          buffer += chunk.toString()
          
          // 处理 SSE 数据流
          const lines = buffer.split('\n')
          buffer = lines.pop() || '' // 保留不完整的行
          
          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.slice(6).trim()
              
              if (data === '[DONE]') {
                resolve()
                return
              }
              
              try {
                const parsed = JSON.parse(data)
                
                // 更新对话ID
                if (parsed.conversation_id) {
                  this.conversationId = parsed.conversation_id
                }
                
                // 处理消息内容
                if (parsed.event === 'message' && parsed.answer) {
                  onChunk(parsed.answer)
                } else if (parsed.event === 'message_end') {
                  resolve()
                  return
                }
              } catch (e) {
                console.warn('解析 SSE 数据失败:', e)
              }
            }
          }
        })
        
        response.data.on('end', () => {
          resolve()
        })
        
        response.data.on('error', (error) => {
          reject(error)
        })
      })
    } catch (error) {
      console.error('API 调用失败:', error)
      
      // 如果是网络错误或 API 不可用，使用模拟响应
      if (error.code === 'ECONNREFUSED' || error.response?.status >= 500) {
        return this.simulateResponse(message, onChunk)
      }
      
      throw error
    }
  }

  /**
   * 模拟 AI 响应（用于演示或 API 不可用时）
   * @param {string} message - 用户消息
   * @param {function} onChunk - 流式响应回调函数
   * @returns {Promise}
   */
  async simulateResponse(message, onChunk) {
    const responses = [
      '感谢您的问题！我是基于 Dify 的 AI 助手。',
      '这是一个很有趣的问题。让我为您详细解答...',
      '根据您的描述，我建议您可以考虑以下几个方面：\n\n1. 首先分析问题的核心\n2. 制定解决方案\n3. 逐步实施和优化',
      '我理解您的需求。作为 AI 助手，我会尽力为您提供准确和有用的信息。',
      '这个问题涉及多个方面，让我逐一为您分析：\n\n**技术层面：**\n- 需要考虑性能优化\n- 用户体验设计\n- 安全性保障\n\n**实施建议：**\n- 采用渐进式开发\n- 持续测试和改进'
    ]
    
    const response = responses[Math.floor(Math.random() * responses.length)]
    
    // 模拟流式输出
    return new Promise((resolve) => {
      let index = 0
      const interval = setInterval(() => {
        if (index < response.length) {
          const chunk = response.slice(index, index + Math.random() * 5 + 1)
          onChunk(chunk)
          index += chunk.length
        } else {
          clearInterval(interval)
          resolve()
        }
      }, 50 + Math.random() * 100) // 随机延迟模拟真实打字效果
    })
  }

  /**
   * 获取对话历史
   * @returns {Promise<Array>}
   */
  async getConversationHistory() {
    if (!this.conversationId) {
      return []
    }

    try {
      const response = await apiClient.get(`/conversations/${this.conversationId}/messages`, {
        params: {
          user: this.userId,
          limit: 50
        }
      })
      
      return response.data.data || []
    } catch (error) {
      console.error('获取对话历史失败:', error)
      return []
    }
  }

  /**
   * 重置对话
   */
  resetConversation() {
    this.conversationId = null
  }

  /**
   * 获取应用信息
   * @returns {Promise<Object>}
   */
  async getAppInfo() {
    try {
      const response = await apiClient.get('/parameters', {
        params: {
          user: this.userId
        }
      })
      
      return response.data
    } catch (error) {
      console.error('获取应用信息失败:', error)
      return {
        opening_statement: '您好！我是 AI 助手，有什么可以帮助您的吗？',
        suggested_questions: [
          '你好，请介绍一下自己',
          '帮我写一个 Vue 组件',
          '解释一下这段代码',
          '如何优化网站性能？'
        ]
      }
    }
  }
}

// 导出单例实例
export const chatService = new ChatService()

// 导出类以便测试
export { ChatService }
