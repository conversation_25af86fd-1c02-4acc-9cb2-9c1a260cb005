// 应用配置
export const config = {
  // Dify API 配置
  dify: {
    apiBase: import.meta.env.VITE_DIFY_API_BASE || 'https://api.dify.ai/v1',
    apiKey: import.meta.env.VITE_DIFY_API_KEY || '',
    timeout: 30000
  },
  
  // 聊天配置
  chat: {
    maxMessageLength: 2000,
    maxFileSize: 10 * 1024 * 1024, // 10MB
    supportedFileTypes: ['image/*', '.pdf', '.doc', '.docx', '.txt'],
    autoScroll: true,
    showTimestamp: true
  },
  
  // UI 配置
  ui: {
    theme: 'light',
    primaryColor: '#409EFF',
    borderRadius: '12px',
    maxChatWidth: '800px'
  },
  
  // 功能开关
  features: {
    fileUpload: false, // 文件上传功能
    voiceInput: false, // 语音输入功能
    messageRegenerate: true, // 消息重新生成
    messageCopy: true, // 消息复制
    conversationHistory: true // 对话历史
  }
}

// 环境变量验证
export const validateConfig = () => {
  const warnings = []
  
  if (!config.dify.apiKey) {
    warnings.push('VITE_DIFY_API_KEY 未设置，将使用模拟响应')
  }
  
  if (warnings.length > 0) {
    console.warn('配置警告:', warnings)
  }
  
  return warnings
}
