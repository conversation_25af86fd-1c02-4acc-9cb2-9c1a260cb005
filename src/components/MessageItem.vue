<template>
  <div class="message-item" :class="{ 'user-message': isUser, 'assistant-message': !isUser }">
    <div class="message-content">
      <!-- 头像 -->
      <div class="avatar">
        <el-avatar 
          :size="36" 
          :src="isUser ? userAvatar : assistantAvatar"
          :icon="isUser ? 'User' : 'Robot'"
        />
      </div>

      <!-- 消息气泡 -->
      <div class="message-bubble" :class="{ 'error': message.error }">
        <div class="message-header">
          <span class="sender-name">{{ isUser ? '您' : 'AI 助手' }}</span>
          <span class="timestamp">{{ formatTime(message.timestamp) }}</span>
        </div>
        
        <div class="message-text">
          <!-- 用户消息直接显示 -->
          <div v-if="isUser" class="user-text">
            {{ message.content }}
          </div>
          
          <!-- AI 消息支持 Markdown 渲染 -->
          <div v-else class="assistant-text">
            <div 
              v-if="message.content"
              v-html="renderedContent"
              class="markdown-content"
            ></div>
            
            <!-- 流式输入光标 -->
            <span v-if="message.streaming" class="typing-cursor">|</span>
          </div>
        </div>

        <!-- 消息操作 -->
        <div v-if="!isUser && message.content" class="message-actions">
          <el-button 
            size="small" 
            text 
            @click="copyMessage"
            :icon="CopyDocument"
          >
            复制
          </el-button>
          <el-button 
            size="small" 
            text 
            @click="regenerateMessage"
            :icon="RefreshRight"
          >
            重新生成
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { CopyDocument, RefreshRight } from '@element-plus/icons-vue'
import { marked } from 'marked'
import hljs from 'highlight.js'
import 'highlight.js/styles/github.css'

const props = defineProps({
  message: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['regenerate'])

const isUser = computed(() => props.message.type === 'user')

const userAvatar = 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'
const assistantAvatar = 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'

// 配置 marked
marked.setOptions({
  highlight: function(code, lang) {
    if (lang && hljs.getLanguage(lang)) {
      try {
        return hljs.highlight(code, { language: lang }).value
      } catch (err) {}
    }
    return hljs.highlightAuto(code).value
  },
  breaks: true,
  gfm: true
})

// 渲染 Markdown 内容
const renderedContent = computed(() => {
  if (!props.message.content) return ''
  return marked(props.message.content)
})

// 格式化时间
const formatTime = (timestamp) => {
  const now = new Date()
  const messageTime = new Date(timestamp)
  const diff = now - messageTime

  if (diff < 60000) { // 1分钟内
    return '刚刚'
  } else if (diff < 3600000) { // 1小时内
    return `${Math.floor(diff / 60000)}分钟前`
  } else if (diff < 86400000) { // 24小时内
    return messageTime.toLocaleTimeString('zh-CN', { 
      hour: '2-digit', 
      minute: '2-digit' 
    })
  } else {
    return messageTime.toLocaleDateString('zh-CN', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }
}

// 复制消息
const copyMessage = async () => {
  try {
    await navigator.clipboard.writeText(props.message.content)
    ElMessage.success('已复制到剪贴板')
  } catch (err) {
    ElMessage.error('复制失败')
  }
}

// 重新生成消息
const regenerateMessage = () => {
  emit('regenerate', props.message)
}
</script>

<style scoped>
.message-item {
  margin-bottom: 24px;
}

.message-content {
  display: flex;
  gap: 12px;
  max-width: 100%;
}

.user-message .message-content {
  flex-direction: row-reverse;
}

.avatar {
  flex-shrink: 0;
}

.message-bubble {
  max-width: 70%;
  background: white;
  border-radius: 12px;
  padding: 12px 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
}

.user-message .message-bubble {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.message-bubble.error {
  background: #fef0f0;
  border: 1px solid #fbc4c4;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
}

.sender-name {
  font-weight: 600;
  color: #409EFF;
}

.user-message .sender-name {
  color: rgba(255, 255, 255, 0.9);
}

.timestamp {
  color: #999;
  font-size: 11px;
}

.user-message .timestamp {
  color: rgba(255, 255, 255, 0.7);
}

.message-text {
  line-height: 1.6;
  word-wrap: break-word;
}

.user-text {
  font-size: 14px;
}

.assistant-text {
  font-size: 14px;
  color: #333;
}

.markdown-content {
  /* Markdown 样式 */
}

.markdown-content :deep(pre) {
  background: #f6f8fa;
  border-radius: 6px;
  padding: 12px;
  overflow-x: auto;
  margin: 8px 0;
}

.markdown-content :deep(code) {
  background: #f6f8fa;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.markdown-content :deep(blockquote) {
  border-left: 4px solid #dfe2e5;
  padding-left: 16px;
  margin: 8px 0;
  color: #6a737d;
}

.typing-cursor {
  animation: blink 1s infinite;
  font-weight: bold;
  color: #409EFF;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

.message-actions {
  margin-top: 8px;
  display: flex;
  gap: 8px;
}

.message-actions .el-button {
  font-size: 12px;
  padding: 4px 8px;
}
</style>
