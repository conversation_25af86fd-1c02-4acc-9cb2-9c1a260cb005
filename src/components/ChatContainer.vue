<template>
  <div class="chat-container">
    <!-- 头部 -->
    <div class="chat-header">
      <div class="header-content">
        <div class="avatar">
          <el-avatar :size="40" src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png" />
        </div>
        <div class="title-info">
          <h2>AI 助手</h2>
          <p class="subtitle">基于 Dify 的智能对话助手</p>
        </div>
      </div>
    </div>

    <!-- 消息区域 -->
    <div class="messages-container" ref="messagesContainer">
      <div class="messages-wrapper">
        <!-- 欢迎消息 -->
        <div v-if="messages.length === 0" class="welcome-message">
          <div class="welcome-content">
            <el-icon :size="48" color="#409EFF"><ChatDotRound /></el-icon>
            <h3>欢迎使用 AI 助手</h3>
            <p>我是您的智能助手，有什么可以帮助您的吗？</p>
          </div>
        </div>

        <!-- 消息列表 -->
        <MessageItem
          v-for="message in messages"
          :key="message.id"
          :message="message"
        />

        <!-- 打字指示器 -->
        <TypingIndicator v-if="isTyping" />
      </div>
    </div>

    <!-- 输入区域 -->
    <MessageInput
      @send-message="handleSendMessage"
      :loading="isLoading"
    />
  </div>
</template>

<script setup>
import { ref, nextTick, onMounted } from 'vue'
import { ChatDotRound } from '@element-plus/icons-vue'
import MessageItem from './MessageItem.vue'
import MessageInput from './MessageInput.vue'
import TypingIndicator from './TypingIndicator.vue'
import { chatService } from '../services/chatService.js'
import { v4 as uuidv4 } from 'uuid'

const messages = ref([])
const isLoading = ref(false)
const isTyping = ref(false)
const messagesContainer = ref(null)

// 滚动到底部
const scrollToBottom = () => {
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
    }
  })
}

// 处理发送消息
const handleSendMessage = async (content) => {
  if (!content.trim()) return

  // 添加用户消息
  const userMessage = {
    id: uuidv4(),
    content: content.trim(),
    type: 'user',
    timestamp: new Date()
  }
  messages.value.push(userMessage)
  scrollToBottom()

  // 开始加载状态
  isLoading.value = true
  isTyping.value = true

  try {
    // 创建 AI 回复消息
    const aiMessage = {
      id: uuidv4(),
      content: '',
      type: 'assistant',
      timestamp: new Date(),
      streaming: true
    }
    messages.value.push(aiMessage)
    scrollToBottom()

    // 调用 AI 服务
    await chatService.sendMessage(content, (chunk) => {
      // 流式更新消息内容
      aiMessage.content += chunk
      scrollToBottom()
    })

    // 完成流式响应
    aiMessage.streaming = false
  } catch (error) {
    console.error('发送消息失败:', error)
    ElMessage.error('发送消息失败，请重试')
    
    // 添加错误消息
    const errorMessage = {
      id: uuidv4(),
      content: '抱歉，我现在无法回复您的消息。请稍后再试。',
      type: 'assistant',
      timestamp: new Date(),
      error: true
    }
    messages.value.push(errorMessage)
  } finally {
    isLoading.value = false
    isTyping.value = false
    scrollToBottom()
  }
}

onMounted(() => {
  scrollToBottom()
})
</script>

<style scoped>
.chat-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  max-width: 800px;
  margin: 0 auto;
  background: white;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

.chat-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.title-info h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.subtitle {
  margin: 4px 0 0 0;
  opacity: 0.9;
  font-size: 14px;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background: #f8f9fa;
}

.messages-wrapper {
  max-width: 100%;
}

.welcome-message {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 300px;
}

.welcome-content {
  text-align: center;
  color: #666;
}

.welcome-content h3 {
  margin: 16px 0 8px 0;
  color: #333;
}

.welcome-content p {
  margin: 0;
  font-size: 14px;
}
</style>
