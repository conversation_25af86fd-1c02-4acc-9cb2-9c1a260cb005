<template>
  <div class="message-input-container">
    <div class="input-wrapper">
      <!-- 文件上传按钮 -->
      <el-button 
        circle 
        size="large"
        :icon="Paperclip"
        @click="handleFileUpload"
        class="attach-btn"
        :disabled="loading"
      />

      <!-- 输入框 -->
      <div class="input-area">
        <el-input
          v-model="inputText"
          type="textarea"
          :rows="1"
          :autosize="{ minRows: 1, maxRows: 4 }"
          placeholder="输入您的问题..."
          @keydown="handleKeyDown"
          @input="handleInput"
          :disabled="loading"
          class="message-textarea"
          resize="none"
        />
        
        <!-- 字数统计 -->
        <div v-if="inputText.length > 0" class="char-count">
          {{ inputText.length }}/2000
        </div>
      </div>

      <!-- 发送按钮 -->
      <el-button 
        type="primary" 
        circle 
        size="large"
        :icon="loading ? Loading : Promotion"
        @click="handleSend"
        :disabled="!canSend"
        :loading="loading"
        class="send-btn"
      />
    </div>

    <!-- 快捷操作 -->
    <div class="quick-actions" v-if="!loading && inputText.length === 0">
      <el-button 
        v-for="action in quickActions" 
        :key="action.text"
        size="small" 
        plain
        @click="selectQuickAction(action.text)"
        class="quick-action-btn"
      >
        {{ action.text }}
      </el-button>
    </div>

    <!-- 文件上传对话框 -->
    <input 
      ref="fileInput" 
      type="file" 
      style="display: none" 
      @change="handleFileChange"
      accept="image/*,.pdf,.doc,.docx,.txt"
    />
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { Promotion, Paperclip, Loading } from '@element-plus/icons-vue'

const props = defineProps({
  loading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['send-message'])

const inputText = ref('')
const fileInput = ref(null)

// 快捷操作
const quickActions = ref([
  { text: '你好，请介绍一下自己' },
  { text: '帮我写一个 Vue 组件' },
  { text: '解释一下这段代码' },
  { text: '如何优化网站性能？' }
])

// 是否可以发送
const canSend = computed(() => {
  return inputText.value.trim().length > 0 && 
         inputText.value.length <= 2000 && 
         !props.loading
})

// 处理输入
const handleInput = () => {
  // 限制字符数
  if (inputText.value.length > 2000) {
    inputText.value = inputText.value.substring(0, 2000)
    ElMessage.warning('消息长度不能超过2000字符')
  }
}

// 处理键盘事件
const handleKeyDown = (event) => {
  // Ctrl/Cmd + Enter 发送消息
  if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
    event.preventDefault()
    handleSend()
  }
  // 单独 Enter 换行（默认行为）
}

// 发送消息
const handleSend = () => {
  if (!canSend.value) return
  
  const message = inputText.value.trim()
  if (message) {
    emit('send-message', message)
    inputText.value = ''
  }
}

// 选择快捷操作
const selectQuickAction = (text) => {
  inputText.value = text
}

// 处理文件上传
const handleFileUpload = () => {
  fileInput.value?.click()
}

// 处理文件选择
const handleFileChange = (event) => {
  const file = event.target.files[0]
  if (!file) return

  // 检查文件大小 (10MB)
  if (file.size > 10 * 1024 * 1024) {
    ElMessage.error('文件大小不能超过10MB')
    return
  }

  // 这里可以添加文件上传逻辑
  ElMessage.info('文件上传功能开发中...')
  
  // 清空文件输入
  event.target.value = ''
}
</script>

<style scoped>
.message-input-container {
  background: white;
  border-top: 1px solid #e4e7ed;
  padding: 16px 20px;
}

.input-wrapper {
  display: flex;
  align-items: flex-end;
  gap: 12px;
}

.attach-btn {
  color: #909399;
  border-color: #dcdfe6;
}

.attach-btn:hover {
  color: #409EFF;
  border-color: #409EFF;
}

.input-area {
  flex: 1;
  position: relative;
}

.message-textarea {
  border-radius: 20px;
}

.message-textarea :deep(.el-textarea__inner) {
  border-radius: 20px;
  border: 1px solid #dcdfe6;
  padding: 12px 16px;
  font-size: 14px;
  line-height: 1.5;
  resize: none;
  box-shadow: none;
}

.message-textarea :deep(.el-textarea__inner):focus {
  border-color: #409EFF;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.char-count {
  position: absolute;
  bottom: 8px;
  right: 16px;
  font-size: 12px;
  color: #909399;
  background: white;
  padding: 0 4px;
}

.send-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

.send-btn:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.send-btn:disabled {
  background: #c0c4cc;
  border-color: #c0c4cc;
}

.quick-actions {
  margin-top: 12px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.quick-action-btn {
  border-radius: 16px;
  font-size: 12px;
  padding: 6px 12px;
  border-color: #e4e7ed;
  color: #606266;
}

.quick-action-btn:hover {
  border-color: #409EFF;
  color: #409EFF;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .message-input-container {
    padding: 12px 16px;
  }
  
  .input-wrapper {
    gap: 8px;
  }
  
  .attach-btn,
  .send-btn {
    width: 40px;
    height: 40px;
  }
  
  .quick-actions {
    margin-top: 8px;
  }
  
  .quick-action-btn {
    font-size: 11px;
    padding: 4px 8px;
  }
}
</style>
