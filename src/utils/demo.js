// 演示数据和工具函数

export const demoMessages = [
  {
    id: 'demo-1',
    content: '你好！我想了解一下如何使用 Vue.js 开发前端应用。',
    type: 'user',
    timestamp: new Date(Date.now() - 300000) // 5分钟前
  },
  {
    id: 'demo-2',
    content: `你好！很高兴为你介绍 Vue.js 前端开发。

Vue.js 是一个渐进式的 JavaScript 框架，具有以下特点：

## 核心特性

1. **响应式数据绑定** - 数据变化自动更新视图
2. **组件化开发** - 可复用的组件系统
3. **虚拟 DOM** - 高效的渲染性能
4. **简单易学** - 学习曲线平缓

## 基本示例

\`\`\`vue
<template>
  <div>
    <h1>{{ title }}</h1>
    <button @click="count++">点击次数: {{ count }}</button>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const title = ref('Hello Vue!')
const count = ref(0)
</script>
\`\`\`

## 开发建议

- 使用 **Vue 3** + **Composition API**
- 配合 **Element Plus** 等 UI 库
- 使用 **Vite** 作为构建工具

有什么具体问题吗？我可以为你详细解答！`,
    type: 'assistant',
    timestamp: new Date(Date.now() - 240000) // 4分钟前
  }
]

export const demoQuickActions = [
  '介绍一下 Vue.js 的特点',
  '如何创建一个 Vue 组件？',
  '解释一下响应式原理',
  'Vue 3 有哪些新特性？',
  '推荐一些 Vue 生态工具'
]

// 模拟 API 延迟
export const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms))

// 生成随机 ID
export const generateId = () => {
  return 'msg_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
}

// 格式化文件大小
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 检测是否为移动设备
export const isMobile = () => {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
    navigator.userAgent
  )
}

// 复制到剪贴板
export const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text)
    return true
  } catch (err) {
    // 降级方案
    const textArea = document.createElement('textarea')
    textArea.value = text
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    return true
  }
}

// 滚动到元素
export const scrollToElement = (element, behavior = 'smooth') => {
  if (element) {
    element.scrollIntoView({ behavior, block: 'end' })
  }
}
